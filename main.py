from collections.abc import Async<PERSON>enerator
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware

from app.api.api_v1.api import api_router
from app.core.config import APP_CONFIG, get_database, get_discovery_redis
from app.core.exceptions import (
    Discovery<PERSON>rro<PERSON>,
    discovery_exception_handler,
    http_exception_handler,
    validation_exception_handler,
    general_exception_handler
)
from app.utilities.response_handler import StandardResponse, StandardResponseModel


@asynccontextmanager
async def lifespan(app_fast: FastAPI) -> AsyncGenerator[None, None]:
    """Lifespan context manager for FastAPI application."""
    # Startup
    print(f"CreatorVerse Profile Analytics starting up...{app_fast}")

    # Initialize logger first
    logger = APP_CONFIG.initialize_logger()
    logger.info("Logger initialized successfully")
    
    # Initialize database and redis with logger
    try:
        # Reset singletons to ensure fresh initialization
        from app.core_helper.database import AsyncDatabaseDB
        from app.core_helper.redis_client import RedisClient
        AsyncDatabaseDB._instance = None
        RedisClient._instance = None

        # Get database instance and initialize it
        db_conn = get_database()
        await db_conn.initialize()
        logger.info("Database initialized successfully")
        
        # Get redis instance and initialize it  
        redis_client = get_discovery_redis()
        await redis_client.initialize()
        logger.info("Redis initialized successfully")
        
        # Initialize startup tasks
        try:
            # Initialize startup tasks (discovery-specific tasks removed)
            logger.info("Service initialization completed successfully")
        except Exception as startup_error:
            logger.warning(f"Startup tasks failed: {startup_error}")

    except Exception as e:
        logger.error(f"Failed to initialize services: {e}")
        print(f"Failed to initialize services: {e}")

    yield

    # Shutdown
    logger.info("CreatorVerse Profile Analytics shutting down...")

    # Cleanup resources
    try:
        redis_client = get_discovery_redis()
        await redis_client.close()
        logger.info("Redis connection closed")
        
        db_conn = get_database()
        await db_conn.shutdown()
        logger.info("Database connection pool shutdown completed")
        
        logger.info("Shutdown completed successfully")
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")
        print(f"Error during shutdown: {e}")

    print("CreatorVerse Profile Analytics shutting down...")


app = FastAPI(
    title="CreatorVerse Profile Analytics Backend API",
    version="1.0.0",
    description="CreatorVerse Profile Analytics Backend API - Comprehensive creator profile analytics with external data integration",
    lifespan=lifespan
)

# Register exception handlers for centralized error handling
app.add_exception_handler(DiscoveryError, discovery_exception_handler)
app.add_exception_handler(HTTPException, http_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=APP_CONFIG.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*", "ngrok-skip-browser-warning"],
)

# Include API router
app.include_router(api_router, prefix="/v1")


@app.get("/", response_model=StandardResponseModel[dict])
async def root():
    """Root endpoint with standardized response format"""
    return StandardResponse.success(
        data={
            "environment": APP_CONFIG.environ,
            "service": APP_CONFIG.service_name,
            "version": "1.0.0",
            "capabilities": [
                "profile_analytics", 
                "filter_management",
                "audience_insights",
                "sponsored_content_analysis"
            ]
        },
        message="Welcome to CreatorVerse Profile Analytics API"
    )


